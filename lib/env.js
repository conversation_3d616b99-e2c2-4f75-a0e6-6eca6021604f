import { cache } from 'react';
import { APP_CONFIG_DEFAULTS } from '@/app-config';

/**
 * @typedef {import('./types').AppConfig} AppConfig
 * @typedef {import('./types').SandboxConfig} SandboxConfig
 */

export const CONFIG_ENDPOINT = process.env.NEXT_PUBLIC_APP_CONFIG_ENDPOINT;
export const SANDBOX_ID = process.env.SANDBOX_ID;

export const THEME_STORAGE_KEY = 'theme-mode';
export const THEME_MEDIA_QUERY = '(prefers-color-scheme: dark)';

/**
 * Get the origin from headers
 * @param {Headers} headers - The request headers
 * @returns {string} The origin URL
 */
export function getOrigin(headers) {
  const host = headers.get('host');
  const proto = headers.get('x-forwarded-proto') || 'https';
  return `${proto}://${host}`;
}

// https://react.dev/reference/react/cache#caveats
// > React will invalidate the cache for all memoized functions for each server request.
/**
 * Get app configuration with caching
 * @param {string} origin - The origin URL
 * @returns {Promise<AppConfig>} The app configuration
 */
export const getAppConfig = cache(async (origin) => {
  if (CONFIG_ENDPOINT) {
    const sandboxId = SANDBOX_ID ?? origin.split('.')[0];

    try {
      const response = await fetch(CONFIG_ENDPOINT, {
        cache: 'no-store',
        headers: { 'X-Sandbox-ID': sandboxId },
      });

      /** @type {SandboxConfig} */
      const remoteConfig = await response.json();
      /** @type {AppConfig} */
      const config = { ...APP_CONFIG_DEFAULTS };

      for (const [key, entry] of Object.entries(remoteConfig)) {
        if (entry === null) continue;
        if (
          key in config &&
          typeof config[key] === entry.type &&
          typeof config[key] === typeof entry.value
        ) {
          config[key] = entry.value;
        }
      }

      return config;
    } catch (error) {
      console.error('!!!', error);
    }
  }

  return APP_CONFIG_DEFAULTS;
});
