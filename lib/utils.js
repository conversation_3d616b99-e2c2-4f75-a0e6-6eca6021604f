import { clsx } from 'clsx';
import { Room } from 'livekit-client';
import { twMerge } from 'tailwind-merge';

/**
 * Combines class names using clsx and tailwind-merge
 * @param {...any} inputs - Class values to combine
 * @returns {string} Combined class names
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Converts a text stream to a chat message
 * @param {import('@livekit/components-react').TextStreamData} textStream - The text stream data
 * @param {Room} room - The LiveKit room instance
 * @returns {import('@livekit/components-react').ReceivedChatMessage} The chat message
 */
export function transcriptionToChatMessage(textStream, room) {
  return {
    id: textStream.streamInfo.id,
    timestamp: textStream.streamInfo.timestamp,
    message: textStream.text,
    from:
      textStream.participantInfo.identity === room.localParticipant.identity
        ? room.localParticipant
        : Array.from(room.remoteParticipants.values()).find(
            (p) => p.identity === textStream.participantInfo.identity
          ),
  };
}
