name: Lint and B<PERSON>
permissions:
  contents: read
  pull-requests: read
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - name: Use Node.js 22
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: ESLint
        run: pnpm lint

      - name: Prettier
        run: pnpm format:check

      - name: Ensure build succeeds
        run: pnpm build
