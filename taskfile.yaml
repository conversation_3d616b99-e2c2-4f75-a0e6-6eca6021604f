version: '3'
output: interleaved
dotenv: ['.env.local']

tasks:
  post_create:
    desc: 'Runs after this template is instantiated as a Sandbox or Bootstrap'
    cmds:
      - echo -e "\nYour Next.js voice assistant is ready to go!\n"
      - echo -e "To give it a try, run the following commands:\r\n"
      - echo -e "\tcd {{.ROOT_DIR}}\r"
      - echo -e "\tpnpm install\r"
      - echo -e "\tpnpm dev\r\n"

  install:
    interactive: true
    cmds:
      - 'pnpm install'

  dev:
    interactive: true
    cmds:
      - 'pnpm dev'
