import { useCallback, useEffect, useState } from 'react';

/**
 * @typedef {import('@/app/api/connection-details/route').ConnectionDetails} ConnectionDetails
 */

/**
 * Hook to manage connection details for LiveKit room
 * @returns {{connectionDetails: ConnectionDetails | null, refreshConnectionDetails: () => void}}
 */
export default function useConnectionDetails() {
  // Generate room connection details, including:
  //   - A random Room name
  //   - A random Participant name
  //   - An Access Token to permit the participant to join the room
  //   - The URL of the LiveKit server to connect to
  //
  // In real-world application, you would likely allow the user to specify their
  // own participant name, and possibly to choose from existing rooms to join.

  /** @type {[ConnectionDetails | null, React.Dispatch<React.SetStateAction<ConnectionDetails | null>>]} */
  const [connectionDetails, setConnectionDetails] = useState(null);

  const fetchConnectionDetails = useCallback(() => {
    setConnectionDetails(null);
    const url = new URL(
      process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? '/api/connection-details',
      window.location.origin
    );
    fetch(url.toString())
      .then((res) => res.json())
      .then((data) => {
        setConnectionDetails(data);
      })
      .catch((error) => {
        console.error('Error fetching connection details:', error);
      });
  }, []);

  useEffect(() => {
    fetchConnectionDetails();
  }, [fetchConnectionDetails]);

  return { connectionDetails, refreshConnectionDetails: fetchConnectionDetails };
}
