import { Track } from 'livekit-client';
import { useLocalParticipantPermissions } from '@livekit/components-react';

/**
 * Convert track source to protocol number
 * @param {import('livekit-client').Track.Source} source - The track source
 * @returns {number} Protocol number
 */
const trackSourceToProtocol = (source) => {
  // NOTE: this mapping avoids importing the protocol package as that leads to a significant bundle size increase
  switch (source) {
    case Track.Source.Camera:
      return 1;
    case Track.Source.Microphone:
      return 2;
    case Track.Source.ScreenShare:
      return 3;
    default:
      return 0;
  }
};

/**
 * @typedef {Object} PublishPermissions
 * @property {boolean} camera - Camera publish permission
 * @property {boolean} microphone - Microphone publish permission
 * @property {boolean} screenShare - Screen share publish permission
 * @property {boolean} data - Data publish permission
 */

/**
 * Hook to get publish permissions for different track sources
 * @returns {PublishPermissions} The publish permissions
 */
export function usePublishPermissions() {
  const localPermissions = useLocalParticipantPermissions();

  /**
   * Check if source can be published
   * @param {import('livekit-client').Track.Source} source - The track source
   * @returns {boolean} Whether the source can be published
   */
  const canPublishSource = (source) => {
    return (
      !!localPermissions?.canPublish &&
      (localPermissions.canPublishSources.length === 0 ||
        localPermissions.canPublishSources.includes(trackSourceToProtocol(source)))
    );
  };

  return {
    camera: canPublishSource(Track.Source.Camera),
    microphone: canPublishSource(Track.Source.Microphone),
    screenShare: canPublishSource(Track.Source.ScreenShare),
    data: localPermissions?.canPublishData ?? false,
  };
}
