import { useMemo } from 'react';
import {
  useChat,
  useRoomContext,
  useTranscriptions,
} from '@livekit/components-react';
import { transcriptionToChatMessage } from '@/lib/utils';

/**
 * Hook to combine chat messages and transcriptions
 * @returns {{messages: import('@livekit/components-react').ReceivedChatMessage[], send: (message: string) => void}}
 */
export default function useChatAndTranscription() {
  /** @type {import('@livekit/components-react').TextStreamData[]} */
  const transcriptions = useTranscriptions();
  const chat = useChat();
  const room = useRoomContext();

  const mergedTranscriptions = useMemo(() => {
    /** @type {import('@livekit/components-react').ReceivedChatMessage[]} */
    const merged = [
      ...transcriptions.map((transcription) => transcriptionToChatMessage(transcription, room)),
      ...chat.chatMessages,
    ];
    return merged.sort((a, b) => a.timestamp - b.timestamp);
  }, [transcriptions, chat.chatMessages, room]);

  return { messages: mergedTranscriptions, send: chat.send };
}
