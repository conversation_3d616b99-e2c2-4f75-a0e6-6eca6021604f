{"name": "agent-starter-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build-embed-popup-script": "webpack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@livekit/components-react": "^2.9.9", "@phosphor-icons/react": "^2.1.8", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toolbar": "^1.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "livekit-client": "^2.13.3", "livekit-server-sdk": "^2.13.0", "motion": "^12.16.0", "next": "15.4.7", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.28.4", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "babel-loader": "^9.1.3", "css-loader": "^7.1.2", "dotenv-webpack": "^8.1.1", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.5.0", "postcss-loader": "^8.1.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "style-loader": "^4.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "webpack": "^5.100.1", "webpack-cli": "^6.0.1"}, "packageManager": "pnpm@9.15.9"}